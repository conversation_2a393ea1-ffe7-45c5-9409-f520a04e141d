<template>
  <HerbitProfessionalLayout
    :title="skill ? `Skill: ${skill.name}` : 'Skill Details'"
    titleStyle="gradient"
    :showHomeButton="true"
    :showBackButton="true"
    backPath="/list-skills"
  >
    <!-- Loading indicator -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <SpinnerIcon :size="48" />
      <span class="ml-3 text-gray-300">Loading skill details...</span>
    </div>

    <!-- Error message with animation -->
    <transition name="fade">
      <Alert v-if="errorMessage" variant="error" class="mb-6 flex items-center shadow-lg">
        <AlertIcon>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        </AlertIcon>
        <AlertDescription class="flex-grow">
          {{ errorMessage }}
        </AlertDescription>
        <Button @click="errorMessage = ''" variant="ghost" size="xs" class="ml-2 text-red-300 hover:text-white">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </Button>
      </Alert>
    </transition>

    <!-- Success message with animation -->
    <transition name="fade">
      <Alert v-if="successMessage" variant="success" class="mb-6 flex items-center shadow-lg">
        <AlertIcon>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
        </AlertIcon>
        <AlertDescription class="flex-grow">
          {{ successMessage }}
        </AlertDescription>
        <Button @click="successMessage = ''" variant="ghost" size="xs" class="ml-2 text-green-300 hover:text-white">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </Button>
      </Alert>
    </transition>

    <!-- Skill Details -->
    <div v-if="skill && !isLoading" class="w-full max-w-7xl mx-auto px-4">
      <!-- Skill Info Card -->
      <div class="mb-8">
        <Card variant="form" color="blue" hover>
        <h2 class="text-2xl font-semibold text-white mb-4 bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">
          {{ skill.name }}
        </h2>

        <!-- Description Section -->
        <div>
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-gray-300 text-base font-medium">Description</h3>
          </div>
          <div class="bg-gray-800/50 rounded-lg p-5 max-h-[300px] overflow-y-auto custom-scrollbar border border-gray-700/50">
            <div class="text-white max-w-none">
              <p v-for="(paragraph, index) in formattedDescription" :key="index"
                 class="mb-3 leading-relaxed"
                 :class="{
                   'text-cyan-300 font-medium text-lg': isHeading(paragraph) && getHeadingLevel(paragraph) === 3,
                   'text-blue-300 font-bold text-xl': isHeading(paragraph) && getHeadingLevel(paragraph) === 2,
                   'text-purple-300 font-bold text-2xl': isHeading(paragraph) && getHeadingLevel(paragraph) === 1,
                   'text-gray-300': !isHeading(paragraph),
                   'font-medium': paragraph.includes('**') || paragraph.includes('__'),
                   'italic': paragraph.includes('*') || paragraph.includes('_')
                 }">
                {{ isHeading(paragraph) ? paragraph.replace(/^#+\s*/, '') : paragraph }}
              </p>
            </div>
          </div>
        </div>
        </Card>
      </div>

      <!-- Questions Section - Modern Spacious Design -->
      <div class="w-full">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
          <h2 class="text-2xl font-bold text-white mb-4 md:mb-0 bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent">Questions</h2>
          <div class="flex space-x-4">
            <Button
              @click="navigateToListSkills"
              variant="skillBack"
              size="skillButton"
            >
              <span class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Skills
              </span>
            </Button>
            <Button
              @click="generateQuestions"
              :disabled="isGenerating"
              variant="skillGenerate"
              size="skillButton"
              :class="{ 'opacity-75 cursor-not-allowed': isGenerating }"
            >
              <span class="flex items-center">
                <svg v-if="isGenerating" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                {{ isGenerating ? 'Generating...' : 'Generate Questions' }}
              </span>
            </Button>
          </div>
        </div>

        <!-- No questions message -->
        <div v-if="!questions || questions.length === 0" class="text-center py-20 bg-gray-900/30 rounded-2xl backdrop-blur-sm">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-24 w-24 mx-auto text-gray-600 mb-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p class="text-gray-300 text-2xl">No questions found for this skill</p>
          <p class="text-gray-500 mt-4 text-lg">Click the "Generate Questions" button to create questions for this skill.</p>
        </div>

        <!-- Questions List -->
        <div v-else>
          <div class="mb-8">
            <span class="inline-flex items-center justify-center bg-gradient-to-r from-cyan-900/40 to-blue-900/40 text-cyan-300 text-sm font-medium px-6 py-3 rounded-lg backdrop-blur-sm">
              {{ questions.length }} Questions
            </span>
          </div>

          <!-- Modern spacious layout for questions with transparent backgrounds -->
          <div class="grid grid-cols-1 gap-y-16 w-full max-h-[600px] overflow-y-auto custom-scrollbar pr-4">
            <div v-for="(question, index) in sortedQuestions" :key="question.que_id"
                 class="relative">
              <!-- Question header with colored left border -->
              <div class="flex items-start p-5"
                   :class="{
                     'border-l-4 border-l-green-500': question.level === 'easy',
                     'border-l-4 border-l-yellow-500': question.level === 'intermediate',
                     'border-l-4 border-l-orange-500': question.level === 'advanced'
                   }">
                <div class="flex-grow">
                  <div class="mb-2 flex items-center">
                    <span class="text-cyan-400 text-sm font-medium mr-2">Question</span>
                    <span class="bg-gray-800 text-cyan-300 text-xs font-medium px-2.5 py-0.5 rounded-full">{{ index + 1 }}</span>
                  </div>
                  <h3 class="text-white text-xl font-medium leading-relaxed">{{ question.question }}</h3>
                </div>
              </div>

              <!-- Options with 2x2 grid layout (A B on top, C D below) -->
              <div class="ml-9 mb-5 mt-3">
                <div class="grid grid-cols-2 gap-4">
                  <!-- Create an array of option keys and loop through them -->
                  <div v-for="optionKey in Object.keys(question.options)" :key="optionKey"
                       class="flex items-start transition-all duration-200 hover:translate-x-1"
                       :class="{
                         'opacity-90 hover:opacity-100': optionKey !== question.answer
                       }">
                    <div class="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-full mr-3 text-base shadow-md"
                         :class="{
                           'bg-gray-800 text-white': optionKey !== question.answer,
                           'bg-green-500 text-white': optionKey === question.answer
                         }">
                      {{ optionKey.toUpperCase() }}
                    </div>
                    <span class="text-base pt-0.5"
                          :class="{
                            'text-gray-300': optionKey !== question.answer,
                            'text-green-300 font-medium': optionKey === question.answer
                          }">
                      {{ question.options[optionKey] }}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </HerbitProfessionalLayout>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import axios from 'axios';
import { HerbitProfessionalLayout } from '@/components/layout';
import { Card } from '@/components/ui/card';
import { SpinnerIcon } from '@/components/icons';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertIcon } from '@/components/ui/alert';

const route = useRoute();
const router = useRouter();
const skillId = ref(null);
const skill = ref(null);
const questions = ref([]);
const isLoading = ref(true);
const isGenerating = ref(false);
const errorMessage = ref('');
const successMessage = ref('');

// Format the skill description into paragraphs with basic formatting
const formattedDescription = computed(() => {
  if (!skill.value || !skill.value.description) return [];

  // Split by newlines and filter out empty lines
  return skill.value.description
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0);
});

// Sort questions by difficulty level: easy, intermediate, advanced
const sortedQuestions = computed(() => {
  if (!questions.value || questions.value.length === 0) return [];

  // Create a copy of the questions array to avoid modifying the original
  return [...questions.value].sort((a, b) => {
    const difficultyOrder = { 'easy': 1, 'intermediate': 2, 'advanced': 3 };
    return difficultyOrder[a.level] - difficultyOrder[b.level];
  });
});

// Check if a paragraph is a heading (starts with #)
const isHeading = (text) => {
  return text.startsWith('#');
};

// Get the heading level (number of # characters)
const getHeadingLevel = (text) => {
  let level = 0;
  for (let i = 0; i < text.length; i++) {
    if (text[i] === '#') {
      level++;
    } else {
      break;
    }
  }
  return level;
};

// Navigate back to the skills list
const navigateToListSkills = () => {
  router.push('/list-skills');
};

// Fetch skill details and questions
const fetchSkillDetails = async () => {
  isLoading.value = true;
  errorMessage.value = '';
  successMessage.value = '';
  skillId.value = route.params.id;

  try {
    // Validate skill ID
    if (!skillId.value) {
      throw new Error('Skill ID is missing');
    }

    // Fetch skill details
    const skillResponse = await axios.get('/api/admin/skills');

    // Validate response
    if (!skillResponse.data || !Array.isArray(skillResponse.data)) {
      throw new Error('Invalid response format from server');
    }

    const skillsData = skillResponse.data;
    skill.value = skillsData.find(s => s.id === parseInt(skillId.value));

    if (!skill.value) {
      errorMessage.value = `Skill with ID ${skillId.value} not found. Please check the URL or return to the skills list.`;
      isLoading.value = false;
      return;
    }

    // Fetch questions for this skill
    await fetchSkillQuestions();
  } catch (error) {
    // Detailed error handling with status code
    const statusCode = error.response?.status ? `(Status: ${error.response.status})` : '';
    errorMessage.value = `Failed to load skill details ${statusCode}: ${error.response?.data?.detail || error.message || 'Unknown error'}`;
    console.error('Error fetching skill details:', error);
  } finally {
    isLoading.value = false;
  }
};

// Fetch questions for this skill
const fetchSkillQuestions = async () => {
  try {
    const response = await axios.get(`/api/admin/skill-questions/${skillId.value}`);
    questions.value = response.data.questions || [];
    // Clear any previous error messages on successful fetch
    errorMessage.value = '';
  } catch (error) {
    if (error.response && error.response.status === 404) {
      // No questions found is not an error, just an empty state
      questions.value = [];
      console.log('No questions found for this skill. This is normal for new skills.');
    } else {
      // Detailed error message with status code if available
      const statusCode = error.response?.status ? `(Status: ${error.response.status})` : '';
      errorMessage.value = `Failed to fetch questions ${statusCode}: ${error.response?.data?.detail || error.message || 'Unknown error'}`;
      console.error('Error fetching questions:', error);
    }
  }
};

// Generate questions for this skill
const generateQuestions = async () => {
  isGenerating.value = true;
  errorMessage.value = '';
  successMessage.value = '';

  try {
    // Validate skill data before making the request
    if (!skill.value || !skill.value.id) {
      throw new Error('Skill data is missing or invalid');
    }

    // Additional validation
    if (!skill.value.description || skill.value.description.trim().length < 20) {
      throw new Error('Skill description must be at least 20 characters long for effective question generation');
    }

    const response = await axios.post('/api/admin/generate-skill-questions', {
      skillId: skill.value.id,
      skillName: skill.value.name,
      skillDescription: skill.value.description
    });

    // Validate response structure
    if (!response.data) {
      throw new Error('Invalid response from server');
    }

    // Check if the operation was actually successful
    if (response.data.success === false) {
      throw new Error(response.data.message || 'Question generation failed');
    }

    // Show success message with count if available
    const generatedCount = response.data?.count || response.data?.questionCount || '';
    const countText = generatedCount ? ` (${generatedCount} questions)` : '';
    successMessage.value = response.data.message || `Successfully generated questions for ${skill.value.name}${countText}`;

    // Refresh the questions list to show the new questions
    await fetchSkillQuestions();

    // Clear any previous error messages on successful generation
    errorMessage.value = '';

  } catch (error) {
    console.error('Error generating questions:', error);

    // Handle different types of errors with specific messages
    if (error.response) {
      // Server responded with an error status
      const status = error.response.status;
      const detail = error.response.data?.detail || error.response.data?.message || 'Unknown server error';

      switch (status) {
        case 400:
          errorMessage.value = `Invalid request: ${detail}`;
          break;
        case 404:
          errorMessage.value = `Skill not found: ${detail}`;
          break;
        case 500:
          errorMessage.value = `Server error: ${detail}`;
          break;
        default:
          errorMessage.value = `Error (${status}): ${detail}`;
      }
    } else if (error.request) {
      // Network error - no response received
      errorMessage.value = 'Network error: Unable to connect to the server. Please check your internet connection and try again.';
    } else {
      // Client-side error or validation error
      errorMessage.value = error.message || 'An unexpected error occurred while generating questions';
    }

    // Clear any success message on error
    successMessage.value = '';

    // Show error for 10 seconds then clear (increased from 5 seconds for better UX)
    setTimeout(() => {
      if (errorMessage.value && (errorMessage.value.includes('Invalid request') ||
          errorMessage.value.includes('Server error') ||
          errorMessage.value.includes('Network error'))) {
        errorMessage.value = '';
      }
    }, 10000);
  } finally {
    isGenerating.value = false;
  }
};

onMounted(() => {
  fetchSkillDetails();
});
</script>

<style scoped>
/* Custom scrollbar for overflow areas */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.5);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, rgba(6, 182, 212, 0.5), rgba(59, 130, 246, 0.5));
  border-radius: 4px;
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, rgba(6, 182, 212, 0.7), rgba(59, 130, 246, 0.7));
}

/* Add some padding to the right of the content to prevent overlap with scrollbar */
.custom-scrollbar {
  padding-right: 4px;
  scrollbar-width: thin; /* For Firefox */
  scrollbar-color: rgba(6, 182, 212, 0.5) rgba(31, 41, 55, 0.5); /* For Firefox */
}

/* Fade transition for error and success messages */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.5s, transform 0.5s;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style>
