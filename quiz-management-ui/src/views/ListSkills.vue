<template>
  <HerbitProfessionalLayout
    title="Skills List"
    titleStyle="gradient"
    :showHomeButton="true"
    :showBackButton="true"
    backPath="/"
  >
    <!-- Skills Table Card -->
    <div class="max-w-5xl mx-auto">
      <Card variant="table" color="blue" hover>
        <!-- Loading indicator -->
        <div v-if="isLoading" class="flex justify-center items-center py-12">
          <SpinnerIcon :size="48" />
          <span class="ml-3 text-gray-300">Loading skills...</span>
        </div>

        <!-- Error message -->
        <div v-if="errorMessage" class="bg-red-900/50 border border-red-700 text-red-200 px-4 py-3 rounded-lg mb-6 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          {{ errorMessage }}
        </div>

        <!-- Success message -->
        <div v-if="successMessage" class="bg-green-900/50 border border-green-700 text-green-200 px-4 py-3 rounded-lg mb-6 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
          </svg>
          {{ successMessage }}
        </div>

        <!-- No skills message -->
        <div v-if="!isLoading && !errorMessage && skills.length === 0" class="text-center py-12">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
          </svg>
          <p class="text-gray-400 text-lg">No skills found</p>
          <Button
            @click="navigateTo('/create-skill')"
            variant="skillAdd"
            size="skillButton"
            class="mt-4"
          >
            <span class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Create a Skill
            </span>
          </Button>
        </div>

        <!-- Skills table -->
        <div v-if="!isLoading && skills.length > 0" class="relative">
          <div class="flex justify-between items-center mb-4">
            <p class="text-gray-400">Total skills: {{ skills.length }}</p>
            <Button
              @click="navigateTo('/create-skill')"
              variant="skillAdd"
              size="sm"
              class="px-3 py-1.5 text-sm"
            >
              <span class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Add Skill
              </span>
            </Button>
          </div>

          <div class="overflow-x-auto hide-scrollbar" style="overflow-y: hidden;">
            <table class="w-full text-left border-collapse">
            <thead>
              <tr class="border-b border-gray-700">

                <th class="py-3 px-4 text-gray-300 font-medium">Name</th>
                <th class="py-3 px-4 text-gray-300 font-medium">Description</th>
                <th class="py-3 px-4 text-gray-300 font-medium">
                  <span class="cursor-help">
                    Question Count
                  </span>
                </th>
                <th class="py-3 px-4 text-gray-300 font-medium text-right">Details</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(skill, index) in skills" :key="skill.id"
                  :class="index % 2 === 0 ? 'bg-gray-800/30' : ''"
                  class="border-b border-gray-800">

                <td class="py-3 px-4 text-white font-medium">{{ skill.name }}</td>
                <td class="py-3 px-4 text-gray-300">
                  <div class="max-h-24 overflow-y-auto pr-2 custom-scrollbar">
                    {{ truncateDescription(skill.description) }}
                    <span v-if="isLongDescription(skill.description)" class="text-cyan-400 text-xs ml-1 cursor-pointer" @click="navigateToSkillDetail(skill)">
                      (View more)
                    </span>
                  </div>
                </td>
                <td class="py-3 px-4 text-gray-300">
                  <div class="flex items-center">
                    <span
                      :title="`${skill.questionCount || 0} questions generated`"
                      class="cursor-help"
                    >
                      {{ skill.questionCount || 0 }}
                    </span>
                  </div>
                </td>
                <td class="py-3 px-4 text-right">
                  <Button
                    @click="navigateToSkillDetail(skill)"
                    variant="skillNav"
                    size="skillNav"
                    title="View Skill Details"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                  </Button>
                </td>
              </tr>
            </tbody>
          </table>
          </div>
        </div>
      </Card>
    </div>


  </HerbitProfessionalLayout>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import { HerbitProfessionalLayout } from '@/components/layout';
import { Card } from '@/components/ui/card';
import { SpinnerIcon } from '@/components/icons';
import { Button } from '@/components/ui/button';

const router = useRouter();
const navigateTo = (path) => {
  router.push(path);
};

// Navigate to skill detail page
const navigateToSkillDetail = (skill) => {
  router.push(`/skill/${skill.id}`);
};

// Helper function to truncate description
const truncateDescription = (description) => {
  if (!description) return '';

  // Remove markdown formatting for the truncated view
  const plainText = description.replace(/[#*_`]/g, '');

  if (plainText.length <= 100) return plainText;
  return plainText.substring(0, 100) + '...';
};

// Check if description is long
const isLongDescription = (description) => {
  return description && description.length > 100;
};

// Data
const skills = ref([]);
const isLoading = ref(true);
const errorMessage = ref('');
const successMessage = ref('');

// Fetch skills from API
const fetchSkills = async () => {
  isLoading.value = true;
  errorMessage.value = '';

  try {
    const response = await axios.get('/api/admin/skills');
    const skillsData = response.data;

    // Fetch question counts for each skill
    await fetchQuestionCounts(skillsData);

    if (!skills.value || skills.value.length === 0) {
      console.log('No skills found or empty array returned');
    }
  } catch (error) {
    console.error('Error fetching skills:', error);

    // Handle different types of errors with specific messages
    if (error.response) {
      // Server responded with an error status
      const status = error.response.status;
      const detail = error.response.data?.detail || error.response.data?.message || 'Unknown server error';

      switch (status) {
        case 500:
          errorMessage.value = `Server error: ${detail}`;
          break;
        default:
          errorMessage.value = `Error (${status}): ${detail}`;
      }
    } else if (error.request) {
      // Network error - no response received
      errorMessage.value = 'Network error: Unable to connect to the server. Please check your internet connection and try again.';
    } else {
      // Client-side error
      errorMessage.value = error.message || 'An unexpected error occurred while fetching skills';
    }
  } finally {
    isLoading.value = false;
  }
};

// Fetch question counts for each skill
const fetchQuestionCounts = async (skillsData) => {
  try {
    const response = await axios.get('/api/admin/skill-question-counts');
    const questionCounts = response.data;

    // Map question counts to skills
    skills.value = skillsData.map(skill => ({
      ...skill,
      questionCount: questionCounts[skill.id] || 0
    }));
  } catch (error) {
    console.error('Error fetching question counts:', error);
    // Still set the skills even if question counts fail
    skills.value = skillsData;
  }
};




onMounted(() => {
  fetchSkills();
});
</script>

<style scoped>
/* Custom scrollbar for description overflow */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(31, 41, 55, 0.5);
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, rgba(6, 182, 212, 0.5), rgba(59, 130, 246, 0.5));
  border-radius: 4px;
  border: 1px solid rgba(75, 85, 99, 0.3);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, rgba(6, 182, 212, 0.7), rgba(59, 130, 246, 0.7));
}

/* Add some padding to the right of the content to prevent overlap with scrollbar */
.custom-scrollbar {
  padding-right: 4px;
}

/* Only hide scrollbars for the main container, not for custom scrollbar elements */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}
</style>
